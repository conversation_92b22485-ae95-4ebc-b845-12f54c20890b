# 第一部分 库
from urllib.parse import quote  # 登录时,把中文账号转码使用
import requests                 # 请求通用,网络
import time                     # 全局通用,时间
import re                       # 全局通用,正则
import keyboard                 # 全局通用,键盘
import json                     # 全局通用,json
import datetime                 # 全局通用,日期时间
from threading import Lock      # 线程锁,用在命令
from concurrent.futures import ThreadPoolExecutor   # 线程池,用于任务调度器
import httpx                    # 异步库,用于pull_online
import asyncio                  # 全局通用,异步库
from bs4 import BeautifulSoup   # 网页解析,用作竞技场页面
# 第二部分 自写代码
from mythread import MyThread, ThreadState   # 系统级线程,全局使用
from default import default_speed, default_reconnect, daily_tb_orders, daily_mh_orders   # 内置默认频率,单位秒
from proxy import get_proxy         # 获取代理
from yzm import get_yzm             # 获取验证码
from js_parser import parse_js_code, ret_packages, js_to_python # 解析js代码
from map import ret_way, MyjRoute   # 寻路
from auth_time import get_time_str  # 获取字符串时间
from mail import send_qq_email  # 邮件发送


class MYJ:
    def __init__(self, config):
        # 内置默认配置
        self.延迟 = default_speed  # 内置默认频率,单位秒
        self.client = httpx.AsyncClient(timeout=None)
        self.重连间隔 = default_reconnect
        # 读取部分配置
        self.服务器 = config['服务器']
        self.账号 = config['账号']
        self.密码 = config['密码']
        self.角色序号 = config['角色序号']
        self.邮箱 = config.get('邮箱', None)  # 邮箱配置，为空时设置为None
        self.代理配置 = config.get('代理配置', None)
        self.命令地址 = f'http://{self.服务器}.pet.imop.com/action.jsp?'
        self.日常拖把开关 = config.get('日常拖把开关', True)
        self.日常礼包开关 = config.get('日常礼包开关', True)
        self.日常竞技场开关 = config.get('日常竞技场开关', False)
        self.日常魔化开关 = config.get('日常魔化开关', False)
        self.活动如意开关 = config.get('活动如意开关', False)
        self.日常乐园开关 = config.get('日常乐园开关', False)
        self.日常银月开关 = config.get('日常银月开关', False)
        self.日常还猪开关 = config.get('日常还猪开关', False)
        self.日常沼泽开关 = config.get('日常沼泽开关', False)
        self.日常小镇开关 = config.get('日常小镇开关', False)
        self.日常诅咒小镇开关 = config.get('日常诅咒小镇开关', False)  # 新增：诅咒小镇日常开关
        self.活动通天开关 = config.get('活动通天开关', False)
        self.活动逆无双开关 = config.get('活动逆无双开关', False)
        self.活动普通无双开关 = config.get('活动普通无双开关', False)  # 新增：普通无双活动开关
        self.活动罗汉开关 = config.get('活动罗汉开关', False)
        self.竞技场开关 = config.get('竞技场开关', False)  # 新增：特殊竞技场开关
        self.闲时任务 = config.get('闲时任务', None)
        self.刷怪配置 = config.get('刷怪配置', {})
        self.技能 = config.get('战斗技能', {})  # 保留兼容性，但将逐步废弃
        self.技能配置 = config.get('技能配置', {})  # 新的技能配置
        self.通天配置 = config.get('通天配置', {})  # 通天活动配置
        self.逆无双配置 = config.get('逆无双配置', {})  # 逆无双活动配置
        self.换装包前缀 = config.get('换装包前缀', None)
        self.卡级配置 = config.get('卡级配置', 80)  # 经验百分比上限，默认80%
        self.竞技场结束命令 = config.get('竞技场结束命令', None)
        self.工具账号配置 = config.get('工具账号配置', None)
        self.背包道具 = []
        self.背包道具更新时间 = None
        self.职业技能 = None
        self.技能列表 = []
        self.技能列表更新时间 = None
        self.等级 = None
        self.宠物名 = None
        self.hp_left = None
        self.sp_left = None
        self.hp_max = None
        self.sp_max = None
        self.经验百分比 = None
        self.最新命令 = None
        self.Npc对话框内容 = None
        self.任务列表 = None
        self.任务列表刷新时间 = None
        self.今日已执行活动 = {}  # 记录今日已执行的活动任务
        self.自身状态 = None
        self.对手状态 = None
        self.通天当前层已击杀 = False
        self.命令线程锁 = Lock()
        self.道具说明信息 = None
        self.道具说明信息更新时间 = None
        self.近十条命令数组 = []
        self.近一百条左下框信息 = []
        self.近一百条右上框信息 = []
        self.近一百条右下框信息 = []
        self.登入标记 = False
        self.proxies = None
        self.cookies = None
        self.petId = None
        self.myUserId = None
        self.validateParam = None
        self.patterns = {
            'npc': re.compile(r"p\._getNpc\(([^)]*)\)"),
            'modify': re.compile(r'<[A-Za-z0-9 ="\':;/_\-.]*>'),
            'petLv': re.compile(r"petLv=(\d+);"),   # 宠物等级
            'nowMap': re.compile(r'nowMap\s*=\s*"([^"]+)"'),    # 当前所在地图
            'room': re.compile(r'room\s*=\s*"([^"]+)"'),    # 当前所在房间
            'hp_left': re.compile(r"<span id='hpLine_left_no'[^>]*>(\d+)</span>"),  # 剩余hp
            'hp_max': re.compile(r"<span id='hpLine_left_max_no'[^>]*>(\d+)</span>"),   # 最大hp
            'sp_left': re.compile(r"<span id='mpLine_left_no'[^>]*>(\d+)</span>"),  # 剩余sp
            'sp_max': re.compile(r"<span id='mpLine_left_max_no'>(\d+)</span>"),    # 最大sp
            'exp': re.compile(r'<\s*span\s+id\s*=\s*["\']?expBai["\']?\s*>(\d+)\s*</\s*span\s*>', re.IGNORECASE),   # 百分比经验
            'join_role': re.compile(r"selectimg\(\s*(\d+)\s*,\s*'click'\s*,\s*(\d+)[^)]*\)\s*.*?"
                                    r'<div\s+align="center"\s+style="position:relative;top:-10px">(.*?)</div>', re.DOTALL),  # 获取角色代码及序号部分
            # 'skills': re.compile(r"p\.cutArray\[(?:[0-5])\]='(perform.*?)';"),  #
            'petName': re.compile(r'petName\s?=\s?"(.*)";'),    # 宠物名
            'myUserId': re.compile(r'myUserId\s?=\s?"(.*)";'),  # id
            'validateParam': re.compile(r'validateParam\s?=\s?"(.*)";'),    # 链接验证参数
            'combat_start': re.compile(r"p\._combat\s*\(", re.IGNORECASE),      # 进入战斗
            'combat_end': re.compile(r"p\.lost\(\s*p\.petWin\.fighter_2\s*\)", re.IGNORECASE),  # 退出战斗
        }
        self.online_url = None
        self.online_thread = None
        self.战斗状态 = False
        self.最后进入战斗时间 = None
        self.Npc列表 = []
        self.Npc可攻击列表 = []
        self.房间 = None
        self.地图 = None
        # 任务协作式切换机制 - 不修改原有函数内部结构
        self.任务中断信号 = False  # 任务中断信号，用于协作式任务切换
        self.当前任务名称 = None    # 当前正在执行的任务名称
        self.任务切换锁 = Lock()    # 任务切换锁，防止并发问题
        # 特殊竞技场定时机制
        self.上次竞技场执行时间 = 0  # 上次执行竞技场的时间戳
        self.竞技场执行间隔 = 420    # 7分钟 = 420秒
        # 任务配置 - 便于维护和扩展
        self.日常任务配置 = [
            {
                '任务名': '拖把',
                '开关属性': 'self.日常拖把开关',
                '执行函数': self.日常_拖把,
                '优先级': 1
            },
            {
                '任务名': '魔化',
                '开关属性': 'self.日常魔化开关',
                '执行函数': self.日常_魔化,
                '优先级': 2
            },
            {
                '任务名': '乐园',
                '开关属性': 'self.日常乐园开关',
                '执行函数': self.日常_乐园,
                '优先级': 3
            },
            {
                '任务名': '诅咒小镇',
                '开关属性': 'self.日常诅咒小镇开关',
                '执行函数': self.日常_诅咒小镇,
                '优先级': 4
            }
        ]

        self.活动任务配置 = [
            {
                '任务名': '通天',
                '星期': [1, 5],  # 周二、周六
                '开始时间': datetime.time(19, 50),
                # '开始时间': datetime.time(8, 50),
                '结束时间': datetime.time(20, 59),
                '开关属性': 'self.活动通天开关',
                '执行函数': self.活动_通天,
                '优先级': 1
            },
            {
                '任务名': '逆无双',
                '星期': [0, 2, 4],  # 周一、三、五
                '开始时间': datetime.time(19, 55),
                '结束时间': datetime.time(20, 59),
                # '星期': [1],  # 周一、三、五
                # '开始时间': datetime.time(8, 55),
                # '结束时间': datetime.time(20, 59),
                '开关属性': 'self.活动逆无双开关',
                '执行函数': self.活动_逆无双,
                '优先级': 2
            },
            {
                '任务名': '普通无双',
                '星期': [0, 2, 4],  # 周一、三、五
                '开始时间': datetime.time(20, 15),
                '结束时间': datetime.time(20, 59),
                # '星期': [1],  # 周一、三、五
                # '开始时间': datetime.time(8, 55),
                # '结束时间': datetime.time(20, 59),
                '开关属性': 'self.活动普通无双开关',
                '执行函数': self.活动_普通无双,
                '优先级': 4
            },
            {
                '任务名': '罗汉',
                '星期': [1, 3],  # 周二、四
                '开始时间': datetime.time(19, 0),
                '结束时间': datetime.time(19, 59),
                '开关属性': 'self.活动罗汉开关',
                '执行函数': self.活动_罗汉,
                '优先级': 3
            },
            {
                '任务名': '如意',
                '时间段': [
                    (datetime.time(12, 0), datetime.time(12, 29), '如意_12'),
                    # (datetime.time(19, 50), datetime.time(19, 51), '如意_test'),    # 测试
                    (datetime.time(17, 30), datetime.time(17, 59), '如意_17'),
                    (datetime.time(21, 0), datetime.time(21, 29), '如意_21'),
                ],
                '开关属性': 'self.活动如意开关',
                '执行函数': self.活动_如意,
                '优先级': 0  # 最高优先级
            }
        ]

        self.闲时任务配置 = [
            {
                '任务名': '禅壹',
                '配置值': '闲时_禅壹',
                '执行函数': self.闲时_禅壹,
                '优先级': 1
            },
            {
                '任务名': '禅伍',
                '配置值': '闲时_禅伍',
                '执行函数': self.闲时_禅伍,
                '优先级': 2
            },
            {
                '任务名': '道壹',
                '配置值': '闲时_道壹',
                '执行函数': self.闲时_道壹,
                '优先级': 3
            },
            {
                '任务名': '打工',
                '配置值': '闲时_打工',
                '执行函数': self.闲时_打工,
                '优先级': 4
            },
            {
                '任务名': '刷怪',
                '配置值': '闲时_刷怪',
                '执行函数': self.闲时_刷怪,
                '优先级': 5
            }
        ]
        
        self.pull_online()

        # 启动技能冷却线程
        self.技能冷却线程 = MyThread(target=self.通用_技能冷却, name=f'skill-cd-{self.账号}')
        self.技能冷却线程.start()

        # 启动技能释放线程
        self.技能释放线程 = MyThread(target=self.通用_技能释放, name=f'skill-cast-{self.账号}')
        self.技能释放线程.start()
        # self.pull_online()  # 初始化完了以后直接保持在线

    def print(self, *args):
        text = ' '.join(str(arg) for arg in args)
        print(f'{self.服务器}=>{self.宠物名}==>{text}')

    def 确认服务器登录状态(self):
        while True:
            self.print('获取代理配置')
            self.proxies = get_proxy(self.代理配置.get('代理账号'), self.代理配置.get('代理密码')) if self.代理配置 else None
            if self.proxies is not None:
                self.print('获取代理配置成功')
                _temp_frequency = 30
                _temp_sleep = 0.5
            else :
                self.print('获取代理配置失败')
                _temp_frequency = 30
                _temp_sleep = 1.51
            for i in range(_temp_frequency):
                res = requests.get(f'http://{self.服务器}.pet.imop.com')
                if res.status_code == 503:
                    self.print(f'服务器{self.服务器}维护未完成')
                    time.sleep(_temp_sleep)
                else:
                    # self.print(res.status_code)
                    self.print(f'服务器{self.服务器}不在维护,可登录')
                    return True

    def login(self):
        # 登录错误码
        _error_code = {
            '0': "登录失败,请重新登录",
            '1': "签名验证失败",
            '2': "时间戳过期",
            '3': "参数为空或格式不正确",
            '4': "用户名密码验证未通过",
            '5': "用户已被锁定",
            '6': "密保未通过",
            '7': "cookie验证未通过",
            '8': "token验证未通过",
            '9': "大区验证未通过",
            '11': "验证码错误",
            '12': "验证码为空",
            '999': "系统异常，登录失败"
        }
        # 登录信息
        self.确认服务器登录状态()
        while True:
            # 登录发送信息
            self.proxies = get_proxy(self.代理配置.get('代理账号'), self.代理配置.get('代理密码')) if self.代理配置 else None
            login_post = {
                'url': f'http://{self.服务器}.pet.imop.com/LoginAction.jsp',
                'cookies': {'mopet_logon': '123'},
                'headers': {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Origin': f'http://{self.服务器}.pet.imop.com',
                    'Referer': f'http://{self.服务器}.pet.imop.com/login.html',
                    'Connection': 'keep-alive',
                    'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
                },
                'data': {
                    'user_name': quote(self.账号, encoding="gbk"),
                    'password': self.密码,
                    'checkcode': get_yzm(proxies=self.proxies)
                },
                'proxies': self.proxies,
            }
            # 登录返回信息
            login_response = requests.post(**login_post)
            if r'document.location="/pet.jsp"' in login_response.text:
                # print(login_response.text)
                self.cookies = login_response.cookies
                self.print('登陆成功')
                return True
            else:
                for _code in _error_code.keys():
                    if f'errCode={_code}"' in login_response.text:
                        if _code == '11' or _code == '999':
                            # 错误码为11,验证码错误,重新获取验证码登录
                            # 错误码为999,封ip,重新获取验证码登录
                            continue
                        else:
                            self.print(f'出现非验证码错误,错误类型为=>{_error_code[_code]}')
                            return False

    def join_role(self):
        # 进入切换角色开始
        _get = {
                'cookies': self.cookies,
                'headers': {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': f'http://{self.服务器}.pet.imop.com',
                        'Referer': f'http://{self.服务器}.pet.imop.com/login.html',
                        'Connection': 'keep-alive',
                        'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
                },
                # 'proxies': self.proxies,
            }
        while True:
            _get['url'] = f'http://{self.服务器}.pet.imop.com/action.jsp?action=changerole'
            changerole_response = requests.get(**_get)
            if 'selectPet.jsp' in changerole_response.text:
                break
        while True:
            _get['url'] = f'http://{self.服务器}.pet.imop.com/pet.jsp'
            join_response = requests.get(**_get)
            if 'selectimg' in join_response.text:
                break
        # 进入切换角色完成
        role_boxes = self.patterns['join_role'].findall(join_response.text)
        for role in role_boxes:
            self.print(f"""角色序号[{role[0]}]角色名[{role[2]}]角色id[{role[1]}]""")
        for role in role_boxes:
            if int(role[0]) == int(self.角色序号):
                self.petId = role[1]
                _pet_url = f'http://{self.服务器}.pet.imop.com/pet.jsp?petid={self.petId}'
                break
        # petId获取成功
        # 进入角色
        while True:
            role_get = {
                'url': f'http://{self.服务器}.pet.imop.com/pet.jsp?petid={self.petId}',
                'cookies': self.cookies,
            }
            role_response = requests.get(**role_get)
            if 'showWorldMap' in role_response.text:
                break
        # 进入角色完成
        self.print('进入角色完成')
        # 初始化属性
        self.refresh_info()
        return True

    def refresh_info(self):
        while True:
            response = requests.get(url=f'http://{self.服务器}.pet.imop.com/pet.jsp?petid={self.petId}', cookies=self.cookies)
            t = response.text
            try:
                self.等级 = self.patterns['petLv'].search(t).group(1)
                self.地图 = self.patterns['nowMap'].search(t).group(1)
                if self.地图 is None:
                    continue
                self.房间 = self.patterns['room'].search(t).group(1)
                if self.地图 is None:
                    continue
                self.宠物名 = self.patterns['petName'].search(t).group(1)
                self.myUserId = self.patterns['myUserId'].search(t).group(1)
                self.validateParam = self.patterns['validateParam'].search(t).group(1)
                self.hp_left = self.patterns['hp_left'].search(t).group(1)
                self.sp_left = self.patterns['sp_left'].search(t).group(1)
                self.hp_max = self.patterns['hp_max'].search(t).group(1)
                self.sp_max = self.patterns['sp_max'].search(t).group(1)
                self.经验百分比 = int(re.compile(r'<\s*span\s+id\s*=\s*["\']?expBai["\']?\s*>(\d+)\s*</\s*span\s*>', re.IGNORECASE).search(t).group(1))
                if self.经验百分比 >= self.卡级配置:
                    self.print(f'经验达到上限{self.卡级配置}%, 卡住经验')
                    send_qq_email(self.邮箱, f'账号{self.账号}经验达到上限{self.卡级配置}%, 卡住经验')
                    self.命令线程锁.acquire()
                self.online_url = f'http://{self.服务器}.pet.imop.com:8080/io/{self.myUserId}&{self.validateParam}'
                self.print('初始化属性')
                return
            except:
                pass

    def pull_online(self):
        
        async def _pull_online():
            while True:
                if self.login():
                    if self.join_role():
                        try:
                            async with self.client.stream("GET", url=self.online_url, cookies=self.cookies) as response:
                                self.登入标记 = True
                                async for line in response.aiter_text():
                                    _function_list = parse_js_code(line)# 解析js代码
                                    for _func in _function_list:
                                        # self.print(_func)
                                        match _func['function_name']:
                                            case _ if _func['function_name'] in [
                                                # 不处理函数列表
                                                'initWorker',   # 初始化函数
                                                # 'cls',          # 清空房间
                                                # 'cps',          # 清空房间
                                                'offOpenWin',   # 关闭窗口
                                                'clsMes',       # Npc对话框清空
                                                '_roomDesc',     # 房间描述
                                                'reOnlineNum',  # 在线状态
                                                'addUser',      # 当前房间增加角色
                                                'delUser',      # 当前房间删除角色
                                                'showRen',      # Npc图片
                                                'closeRen',     # Npc图片
                                                'showAlert',    # 白底小提示框
                                                'win',          # 战斗胜利,但是太快反应会卡指令,所以不作为结束战斗依据
                                                'closeBossZhanDouInfo',     # 疑似战斗结束,但是不能用,同上
                                                # '_showMiracle', # 无关痛痒,不知道是啥
                                                'showRenPic',   # NPC图片
                                                'showRenBigPic',   # NPC图片
                                                '_look',        # 被人观察,不处理
                                                'lost',         # 伪脱战,不处理
                                                'showLiLian',   # 历练,没必要处理
                                            ]:
                                                continue
                                            case '_combat':      # 原判断进站
                                                self.战斗状态 = True
                                                self.最后进入战斗时间 = time.time()
                                                self.print('进入战斗')
                                            case _ if _func['function_name'] in ['_showMiracle', 'cls', 'cps']:
                                                self.战斗状态 = False
                                                self.自身状态 = None    # 脱战清空状态
                                                self.对手状态 = None    # 脱战清空状态
                                            case 'addCM':
                                                局_信息 = get_time_str() + '=>' + _func['parameters'][0]['value']
                                                self.近一百条右上框信息.append(局_信息)
                                                self.print(f'CM右上框:{局_信息}')
                                                if len(self.近一百条右上框信息) > 100:
                                                    self.近一百条右上框信息.pop(0)
                                            case 'addRM':
                                                局_信息 = get_time_str() + '=>' + _func['parameters'][0]['value']
                                                self.近一百条左下框信息.append(局_信息)
                                                self.print(f'RM左下框:{局_信息}')
                                                if len(self.近一百条左下框信息) > 100:
                                                    self.近一百条左下框信息.pop(0)
                                            case 'addMY':
                                                局_信息 = get_time_str() + '=>' + _func['parameters'][0]['value']
                                                self.近一百条右下框信息.append(局_信息)
                                                self.print(f'MY右下框:{局_信息}')
                                                if len(self.近一百条右下框信息) > 100:
                                                    self.近一百条右下框信息.pop(0)
                                            case 'addMessage':
                                                match _func['parameters'][0]['value']:
                                                    case 'roomReader':
                                                        局_信息 = get_time_str() + '=>' + _func["parameters"][1]["value"]
                                                        if '你向' in 局_信息 and '发起攻击!' in 局_信息:
                                                            self.战斗状态 = True
                                                            self.最后进入战斗时间 = time.time()
                                                            self.print('进入战斗')
                                                        self.print(f'Message左下框:{局_信息}')
                                                        self.近一百条左下框信息.append(局_信息)
                                                        if len(self.近一百条左下框信息) > 100:
                                                            self.近一百条左下框信息.pop(0)
                                                    case _:
                                                        self.print('待处理addMessage')
                                            case '_showFightStatus':
                                                if _func['parameters'][0]['value'] == 'fighter_1_status':
                                                    self.自身状态 = _func['parameters'][1]['value']
                                                    self.print(f'战斗-自身状态:{self.自身状态}')
                                                if _func['parameters'][0]['value'] == 'fighter_2_status':
                                                    self.对手状态 = _func['parameters'][1]['value']
                                                    self.print(f'战斗-对手状态:{self.对手状态}')
                                            case 'state':
                                                self.print('战斗相关,暂不处理')
                                                self.战斗状态 = True
                                            case _ if _func['function_name'] in ['att1', 'att2']:
                                                self.战斗状态 = True
                                                self.print('战斗相关,暂不处理')
                                            case 'addNpcs':
                                                _s = _func['parameters'][0]['value']
                                                self.Npc列表 = [i.replace("'", '').replace(" ", '').split(',') for i in self.patterns['npc'].findall(_s)]
                                                self.Npc可攻击列表 = [i for i in self.Npc列表 if (i[-2] != 'true') and ('尸体' not in i[3]) and ('战斗中' not in i[3])]
                                            case 'setRoom':
                                                self.房间 = _func['parameters'][0]['value']
                                                self.print('房间', self.房间)
                                            case 'changeMap':
                                                self.地图 = _func['parameters'][0]['value']
                                                self.print('地图', self.地图)
                                            case 'setMaxHP':
                                                self.hp_max = _func['parameters'][0]['value']
                                            case 'setMaxSP':
                                                self.sp_max = _func['parameters'][0]['value']
                                            case 'setLine':
                                                match _func['parameters'][0]['value']:
                                                    case 'hpLine_left':
                                                        局_数据 = _func['parameters'][-1]['value']
                                                        if 局_数据 == '-':
                                                            continue
                                                        self.hp_left = int(局_数据)
                                                    case 'mpLine_left':
                                                        局_数据 = _func['parameters'][-1]['value']
                                                        if 局_数据 == '-':
                                                            continue
                                                        self.sp_left = int(_func['parameters'][-1]['value'])
                                                    case 'hpLine_right':
                                                        pass
                                                    case 'mpLine_right':
                                                        pass
                                            case 'beiDing':
                                                self.print(f'被顶号:ip{_func["parameters"][0]['value']},延迟{self.重连间隔}秒后重登')
                                                time.sleep(self.重连间隔)
                                            case 'setFightTaskImg':
                                                # 无效,这是左上角的图标.不作为释放技能参考,且无法准确判断技能,如十字斩和强打是一个图标
                                                continue
                                            case 'showLeftTalk':
                                                局_技能名称 = _func['parameters'][0]['value'][:-1]
                                                try:
                                                    self.技能配置[局_技能名称]['剩余CD'] = self.技能配置[局_技能名称]['CD'] + 1
                                                except:
                                                    pass
                                            case 'showRightTalk':
                                                self.print(_func)
                                            case _ if _func['function_name'] in ['showI', 'showIHide']:
                                                self.背包道具 = ret_packages(_func)
                                                self.背包道具更新时间 = time.time()
                                            case '_skillsubs':
                                                self.技能列表 = eval(_func['parameters'][0]['value'].replace('false', 'False').replace('true','True'))
                                                self.技能列表更新时间 = time.time()
                                            case 'setLv':
                                                self.等级 = int(_func['parameters'][0]['value'])
                                            case 'setExp':
                                                self.经验百分比 = int(round(int(_func['parameters'][0]['value'])/int(_func['parameters'][2]['value']),4) * 100)
                                                if self.经验百分比 >= self.卡级配置:
                                                    self.print(f'经验达到上限{self.卡级配置}%, 卡住经验')
                                                    if self.邮箱:
                                                        send_qq_email(self.邮箱, f'账号{self.账号}经验达到上限{self.卡级配置}%, 卡住经验')
                                                    return
                                            case '_petinfo':
                                                continue
                                            case 'showAllotWin':
                                                print(_func)
                                                continue
                                            case 'addNPCC':
                                                self.Npc对话框内容 = _func['parameters'][0]['value']
                                                self.print('Npc对话框:', self.Npc对话框内容)
                                            case 'showTask':
                                                _t = _func['parameters'][0]['value']
                                                self.任务列表 = js_to_python(_t)
                                                self.任务列表刷新时间 = time.time()
                                            case 'showItemDesc':
                                                self.道具说明信息 = {
                                                    'name': _func['parameters'][0]['value'],
                                                    'description': _func['parameters'][2]['value']
                                                }
                                                self.print(self.道具说明信息)
                                                self.道具说明信息更新时间 = time.time()
                                            case 'yDeal':
                                                # 这里是交易
                                                continue
                                            case _:
                                                self.print('未处理格式')
                                                self.print(_func)
                        except Exception as e:
                            with open('error.txt', 'a', encoding='utf-8') as f:
                                    f.write(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S') + '=>' + str(e) + '\n')
                await asyncio.sleep(60)

        def _thread_worker():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(_pull_online())

        MyThread(target=_thread_worker, daemon=True).start()

    def send_orders(self, orders:str, delay=None):
        with self.命令线程锁:
            # print(self.cookies)
            orders = orders.split('|')
            self.print(orders)
            for order in orders:
                if order == '':
                    continue
                try:
                    self.最新命令 = order
                    # 更新近十条命令数组
                    self.近十条命令数组.append(order)
                    if len(self.近十条命令数组) > 10:
                        self.近十条命令数组.pop(0)

                    res = requests.post(
                        url=self.命令地址,
                        cookies=self.cookies,
                        data={
                            'action': 'inputCommand',
                            'inputCommand': ('/' + order).encode('gbk'),
                        }
                    )
                    # print(res.text)
                except Exception as e:
                    self.print(e)
                if delay is None:
                    time.sleep(self.延迟)
                else:
                    time.sleep(delay)

    def gto(self, room):
        while True:
            while self.地图 is None or self.房间 is None:
                self.refresh_info()
                time.sleep(0.1)
            寻路起点 = self.地图 + '|' + self.房间
            self.print(f'从{寻路起点}到{room}')
            _way = ret_way(寻路起点, room)
            if _way != '':
                self.print(_way)
            self.send_orders(f'relive|look|{_way}')
            if 寻路起点 == room:
                break
            time.sleep(0.1)

    def 通用_技能释放(self):
        """通用技能释放管理 - 根据当前状态自动选择合适的技能配置进行释放"""
        # 初始化技能剩余CD - 使用新的技能配置
        for key in self.技能配置.keys():
            self.技能配置[key]['剩余CD'] = 0

        while True:
            # 最外层循环,保证一直执行
            if self.战斗状态:
                # 动态获取当前应该使用的技能释放列表
                局_技能释放列表 = self._获取当前技能释放列表()

                # 遍历技能释放列表
                for 局_技能名称 in 局_技能释放列表:
                    if not self.战斗状态:
                        break
                    # 在技能配置中查找该技能
                    if 局_技能名称 in self.技能配置:
                        局_技能属性 = self.技能配置[局_技能名称]
                        # 检查CD和状态条件
                        if 局_技能属性['剩余CD'] <= 0 and (self.自身状态 is None or ('昏迷' not in self.自身状态) and ('沉默' not in self.自身状态)):
                            self.send_orders(f'perform {局_技能名称}')
                            time.sleep(局_技能属性['前摇'])     # 前摇抬手释放了再计算冷却
                            # 局_技能属性['剩余CD'] = 局_技能属性['CD']     # 冷却由showLeftTalk重制
                            time.sleep(局_技能属性['后摇'])
            time.sleep(0.1) # 防止CPU占用过高

    def _获取当前技能释放列表(self):
        """根据当前任务类型和状态，动态获取应该使用的技能释放列表"""
        # 优先使用当前任务名称进行精确判断
        if self.当前任务名称:
            # 闲时刷怪任务
            if '闲时_刷怪' in self.当前任务名称 and '释放技能列表' in self.刷怪配置:
                return self.刷怪配置['释放技能列表']

            # 活动通天任务
            elif '活动_通天' in self.当前任务名称 and '释放技能列表' in self.通天配置:
                return self.通天配置['释放技能列表']

            # 活动逆无双任务
            elif '活动_逆无双' in self.当前任务名称 and '释放技能列表' in self.逆无双配置:
                return self.逆无双配置['释放技能列表']

            # TODO: 活动罗汉任务
            # elif '活动_罗汉' in self.当前任务名称 and '释放技能列表' in self.罗汉配置:
            #     return self.罗汉配置['释放技能列表']
            else:
                return self.刷怪配置['释放技能列表']

        # 备用判断：基于闲时任务配置
        if self.闲时任务 == '闲时_刷怪' and '释放技能列表' in self.刷怪配置:
            return self.刷怪配置['释放技能列表']

        # 备用判断：基于房间名称（用于手动操作或特殊情况）
        elif '通天塔' in str(self.房间) and '释放技能列表' in self.通天配置:
            return self.通天配置['释放技能列表']

        elif '逆无双' in str(self.房间) and '释放技能列表' in self.逆无双配置:
            return self.逆无双配置['释放技能列表']

        # 兼容旧配置：如果没有新配置，使用旧的技能字典
        else:
            return self.刷怪配置['释放技能列表']

    def 通用_技能冷却(self):
        """通用技能冷却管理 - 每秒减少所有技能的剩余CD"""
        while True:
            # 使用新的技能配置进行CD冷却
            for key, value in self.技能配置.items():
                if value['剩余CD'] > 0:
                    value['剩余CD'] -= 1
            time.sleep(1)

    def _任务包装器(self, 任务函数, 任务名称):
        """
        任务包装器 - 为原有任务函数提供协作式中断机制
        不修改原有函数内部结构，通过外围包装实现优雅切换
        """
        with self.任务切换锁:
            self.当前任务名称 = 任务名称
            self.任务中断信号 = False

        self.print(f'开始执行任务: {任务名称}')

        try:
            # 执行原有任务函数，完全不修改其内部结构
            任务函数()
        except Exception as e:
            self.print(f'任务 {任务名称} 执行异常: {e}')
        finally:
            with self.任务切换锁:
                self.当前任务名称 = None
                self.任务中断信号 = False
            self.print(f'任务 {任务名称} 已结束')

    def _检查任务中断信号(self):
        """
        检查任务中断信号 - 供需要协作式中断的任务调用
        返回True表示需要中断当前任务
        """
        return self.任务中断信号

    def _发送任务中断信号(self, 目标任务名称=None):
        """
        发送任务中断信号 - 请求当前任务优雅退出
        """
        with self.任务切换锁:
            if self.当前任务名称:
                if 目标任务名称 is None or self.当前任务名称 == 目标任务名称:
                    self.print(f'发送中断信号给任务: {self.当前任务名称}')
                    self.任务中断信号 = True
                    return True
            return False

    def _等待任务优雅退出(self, 最大等待秒数=30):
        """
        等待当前任务优雅退出
        """
        等待开始时间 = time.time()
        while self.当前任务名称 and (time.time() - 等待开始时间) < 最大等待秒数:
            time.sleep(0.5)

        if self.当前任务名称:
            self.print(f'任务 {self.当前任务名称} 未在 {最大等待秒数} 秒内退出')
            return False
        else:
            self.print('任务已优雅退出')
            return True

    def _检查并执行竞技场(self):
        """
        检查是否需要执行特殊竞技场任务
        返回True表示执行了竞技场，False表示未执行
        """
        # 首先检查竞技场开关是否开启
        if not self.竞技场开关:
            return False

        当前时间 = time.time()

        # 检查是否到了执行时间
        if 当前时间 - self.上次竞技场执行时间 >= self.竞技场执行间隔:
            self.print('检测到竞技场执行时间到达')

            # 如果在战斗中，等待战斗结束
            if self.战斗状态:
                self.print('当前在战斗中，等待战斗结束后执行竞技场')
                while self.战斗状态:
                    time.sleep(1)
                    # 添加超时保护，避免无限等待
                    if time.time() - 当前时间 > 300:  # 最多等待5分钟
                        self.print('等待战斗结束超时，跳过本次竞技场执行')
                        return False

            self.print('开始执行特殊竞技场任务')
            try:
                self.特殊_竞技场()
                self.上次竞技场执行时间 = time.time()  # 更新执行时间
                self.print('特殊竞技场任务执行完成')
                return True
            except Exception as e:
                self.print(f'特殊竞技场执行异常: {e}')
                return False

        return False

    def 至高_经验锁定(self):
        """
        最高权限经验锁定功能
        - 从配置读取经验百分比上限，默认80%
        - 当预设值>=100时不锁定经验
        - 达到上限时获取命令线程锁，阻止所有命令发送
        """
        # 获取经验上限配置，默认80%
        经验上限 = self.卡级配置 if self.卡级配置 is not None else 80

        # 如果配置>=100，表示不锁定经验
        if 经验上限 >= 100:
            return

        # 检查当前经验百分比
        if self.经验百分比 is not None and self.经验百分比 >= 经验上限:
            self.print(f'⚠️ 经验锁定触发！当前经验: {self.经验百分比}%, 上限: {经验上限}%')
            self.print('🔒 获取命令线程锁，停止所有操作...')

            # 获取命令线程锁，阻止所有send_orders调用
            self.命令线程锁.acquire()

            self.print('✅ 经验已锁定，所有命令已停止发送')
            self.print('💡 如需解锁，请修改配置文件中的卡级配置或手动释放锁')

            # 注意：这里故意不释放锁，需要外部干预才能解锁
            # 如果需要自动解锁机制，可以在这里添加相关逻辑

    def 日常_拖把(self):
        _markpoint = self.地图 + '|' + self.房间        # 标记当前位置
        self.gto('map.mopcity|驿站')
        self.send_orders(daily_tb_orders)
        self.gto(_markpoint)        # 回到标记点

    def 日常_魔化(self):
        _markpoint = self.地图 + '|' + self.房间        # 标记当前位置
        self.gto('map.jixiedao|机械岛接待室')
        self.send_orders(daily_mh_orders)
        self.gto(_markpoint)

    def 日常_乐园(self):
        def _判断是否完成任务()->bool:
            t = 0
            for s in self.近一百条左下框信息:
                if '任务【物品收集】 (完成:10/10)' in s:
                    t += 1
                if '任务【乐园中的怪物】 (完成:10/10)' in s:
                    t += 1
                if t >= 2:
                    return True
            return False

        def _判断最多的怪物()->str:
            局_返回怪物名 = ''
            局_储存列表 = []
            for 局_房间 in ['正门','缆车','吓人屋','旋转木马','喷泉','小径','碰碰车','快餐店','玩具店']:
                self.send_orders(f'gto {局_房间}')
                局_储存列表 += self.Npc可攻击列表
            局_怪物字典 = {}
            for 局_怪物 in 局_储存列表:
                if 局_怪物[3] in 局_怪物字典:
                    局_怪物字典[局_怪物[3]] += 1
                else:
                    局_怪物字典[局_怪物[3]] = 1
            局_最大值 = 0
            for key, value in 局_怪物字典.items():
                if value > 局_最大值:
                    局_最大值 = value
                    局_返回怪物名 = key
            self.print(局_怪物字典)
            self.print('最多的是:' + 局_返回怪物名)
            return 局_返回怪物名

        self.gto('map.zhutileyuan.city|正门')
        # 确保放弃任务
        self.send_orders('showTask pettask.daily.LeYuanGuaiWu giveup|showTask pettask.daily.WuPinShouJi giveup')
        # 判断最多的怪物名
        局_怪物名称 = _判断最多的怪物()
        # 开始领取任务
        self.gto('map.zhutileyuan.city|正门')
        self.send_orders('beginLeYuanGuaiWu')
        局_NPC对话内容 = self.Npc对话框内容
        # 如果没有这几个字,那么直接就结束了,领不到任务默认做完了
        if '今天要杀死的怪物是' in 局_NPC对话内容:
            while True:
                self.send_orders('beginLeYuanGuaiWu',0.5)
                if '今天要杀死的怪物是' in self.Npc对话框内容 and 局_怪物名称 in self.Npc对话框内容:
                    break
                self.send_orders('showTask pettask.daily.LeYuanGuaiWu giveup',0.5)
            # 循环接取到同怪任务
            while True:
                self.send_orders('beginWuPinShouJi',0.5)
                if '今天要收集的物品是' in self.Npc对话框内容 and 局_怪物名称 in self.Npc对话框内容:
                    break
                self.send_orders('showTask pettask.daily.WuPinShouJi giveup',0.5)
            # 循环杀怪
            self.print(f'乐园成功领取到任务,目标{局_怪物名称}')
            局_临时地图 = ['正门','缆车','吓人屋','旋转木马','喷泉','小径','碰碰车','快餐店','玩具店']
            while not _判断是否完成任务():
                for i in 局_临时地图:
                    # 跑图
                    self.send_orders(f'gto {i}')
                    for npc in self.Npc可攻击列表:
                        if 局_怪物名称 in npc[1]:
                            self.send_orders(f'bar34 {npc[0]} {npc[2]}')
                            while self.战斗状态:
                                time.sleep(0.1)
                        if _判断是否完成任务():
                            break
                    if _判断是否完成任务():
                        break
            self.gto('map.zhutileyuan.city|正门')
            self.send_orders('endLeYuanGuaiWu|endWuPinShouJi')
            self.gto('map.mopcity|驿站')
            return

    def 日常_诅咒小镇(self):
        def _判断是否完成任务()->bool:
            t = 0
            for s in self.近一百条左下框信息:
                if '任务【灵力物品】 (完成:10/10)' in s:
                    t += 1
                if '任务【清理怪物】 (完成:10/10)' in s:
                    t += 1
                if t >= 2:
                    return True
            return False

        def _判断最多的怪物()->str:
            局_返回怪物名 = ''
            局_储存列表 = []
            for 局_房间 in ['钟楼','碎石广场','瞭望塔','磨坊','熔岩之池','库房','小镇空地','死亡矿井','诅咒小镇驿站','民居']:
                self.send_orders(f'gto {局_房间}')
                局_储存列表 += self.Npc可攻击列表
            局_怪物字典 = {}
            for 局_怪物 in 局_储存列表:
                if 局_怪物[3] in 局_怪物字典:
                    局_怪物字典[局_怪物[3]] += 1
                else:
                    局_怪物字典[局_怪物[3]] = 1
            局_怪物字典 = {key: value for key, value in 局_怪物字典.items() if key in ['鬼婴','猪面怪','变异鼠','南瓜妖']}
            局_最大值 = 0
            for key, value in 局_怪物字典.items():
                if value > 局_最大值:
                    局_最大值 = value
                    局_返回怪物名 = key
            self.print(局_怪物字典)
            self.print('最多的是:' + 局_返回怪物名)
            return 局_返回怪物名

        self.gto('map.zuzhouxiaozhen|钟楼')
        # 确保放弃任务
        self.send_orders('showTask pettask.daily.QingLiGuaiWu giveup|showTask pettask.daily.LingLiWuPin giveup')
        # 判断最多的怪物名
        局_怪物名称 = _判断最多的怪物()
        # 开始领取任务
        self.gto('map.zuzhouxiaozhen|钟楼')
        self.send_orders('beginQingLiGuaiWu')
        局_NPC对话内容 = self.Npc对话框内容
        # 如果没有这几个字,那么直接就结束了,领不到任务默认做完了
        if '今天要清理的怪物' in 局_NPC对话内容:
            while True:
                self.send_orders('beginQingLiGuaiWu',0.5)
                if '今天要清理的怪物' in self.Npc对话框内容 and 局_怪物名称 in self.Npc对话框内容:
                    break
                self.send_orders('showTask pettask.daily.QingLiGuaiWu giveup',0.5)
            # 循环接取到同怪任务
            while True:
                self.send_orders('beginLingLiWuPin',0.5)
                if '今天要收集的' in self.Npc对话框内容 and 局_怪物名称 in self.Npc对话框内容:
                    break
                self.send_orders('showTask pettask.daily.LingLiWuPin giveup',0.5)
            # 循环杀怪
            self.print(f'小镇成功领取到任务,目标{局_怪物名称}')
            局_临时地图 = ['钟楼','碎石广场','瞭望塔','磨坊','熔岩之池','库房','小镇空地','死亡矿井','诅咒小镇驿站','民居']
            while not _判断是否完成任务():
                for i in 局_临时地图:
                    # 跑图
                    self.send_orders(f'gto {i}')
                    for npc in self.Npc可攻击列表:
                        if 局_怪物名称 in npc[1]:
                            self.send_orders(f'bar34 {npc[0]} {npc[2]}')
                            while self.战斗状态:
                                time.sleep(0.1)
                        if _判断是否完成任务():
                            break
                    if _判断是否完成任务():
                        break
            self.gto('map.zuzhouxiaozhen|钟楼')
            self.send_orders('endQingLiGuaiWu|endLingLiWuPin')
            self.gto('map.mopcity|驿站')
            return

    def 日常_沼泽(self):
        pass

    def 特殊_竞技场(self):

        def _开始前函数():
            """竞技场挑战开始前执行的函数"""
            self.send_orders('use fitems.pet.other.ShengWangFangDaJing_G|JingJiChang extraTiaoZhan|JingJiChang extraTiaoZhan|JingJiChang extraTiaoZhan|JingJiChang extraTiaoZhan|JingJiChang extraTiaoZhan')

        def _结束后函数():
            """竞技场挑战结束后执行的函数"""
            if self.竞技场结束命令:
                self.send_orders(self.竞技场结束命令)

        def parse_arena_html(html: str) -> dict:
            soup = BeautifulSoup(html, "lxml")  # 需要 pip install lxml

            # ---------- 玩家信息 ----------
            info_span = soup.find(id="jingxianginfo_span")
            text = info_span.get_text("\n", strip=True) if info_span else ""
            text = re.sub(r"每天主动挑战.*?奖励。", "", text, flags=re.S)

            lines = [ln.strip() for ln in text.splitlines() if ln.strip()]
            name = lines[0] if lines else ""

            def find_num(label_pat, default=None, to_int=True):
                m = re.search(label_pat, text)
                if not m:
                    return default
                val = m.group(1).strip()
                if to_int:
                    try:
                        return int(val)
                    except:
                        pass
                return val

            level = find_num(r"等级[:：]\s*([0-9]+)")
            power = find_num(r"战斗力指数[:：]\s*([0-9]+)")
            rank = find_num(r"排名[:：]\s*No\.?\s*([0-9]+)")
            title = find_num(r"称号[:：]\s*([^\n\r]+)", default="", to_int=False)

            score_cur = None
            score_max = None
            m_score = re.search(r"积分[:：]\s*([0-9]+)\s*/\s*([0-9]+)", text)
            if m_score:
                score_cur = int(m_score.group(1))
                score_max = int(m_score.group(2))

            reputation = find_num(r"声望[:：]\s*([0-9]+)")

            refresh_time_el = soup.find(id="refresh_time")
            refresh_time = refresh_time_el.get_text(strip=True) if refresh_time_el else None

            # ---------- 对手信息 ----------
            opponents = []
            table = soup.find("table", class_="EnemyInfo")
            if table:
                for tr in table.find_all("tr", attrs={"align": "center"}):
                    tds = tr.find_all("td")
                    if len(tds) < 4:
                        continue
                    name_o = tds[0].get_text(strip=True)
                    m_lv = re.search(r"(\d+)", tds[1].get_text(strip=True))
                    level_o = int(m_lv.group(1)) if m_lv else None
                    m_rank = re.search(r"(\d+)", tds[2].get_text(strip=True))
                    rank_o = int(m_rank.group(1)) if m_rank else None
                    try:
                        power_o = int(tds[3].get_text(strip=True))
                    except:
                        power_o = None
                    attack_id = None
                    a = tr.find("a", onclick=True)
                    if a:
                        m = re.search(r"attack\((\d+)\)", a["onclick"])
                        if m:
                            attack_id = int(m.group(1))
                    opponents.append({
                        "name": name_o,
                        "level": level_o,
                        "rank": rank_o,
                        "power": power_o,
                        "attack_id": attack_id
                    })

            # ---------- 挑战次数和倒计时 ----------
            challenge_count = None
            extra_count = None
            next_cd = None
            if table:
                m_chal = table.find(string=re.compile(r"挑战次数"))
                if m_chal:
                    tr = m_chal.find_parent("tr")
                    if tr:
                        tds = tr.find_all("td")
                        if len(tds) >= 4:
                            m = re.search(r"(\d+)\s*/\s*(\d+)", tds[1].get_text(strip=True))
                            if m:
                                challenge_count = {"used": int(m.group(1)), "max": int(m.group(2))}
                            m2 = re.search(r"(\d+)\s*/\s*(\d+)", tds[3].get_text(strip=True))
                            if m2:
                                extra_count = {"used": int(m2.group(1)), "max": int(m2.group(2))}
                count_time_el = soup.find(id="countTime")
                if count_time_el:
                    next_cd = count_time_el.get_text(strip=True)

            return {
                "player": {
                    "name": name,
                    "level": level,
                    "power": power,
                    "rank": rank,
                    "title": title,
                    "score": {"current": score_cur, "max": score_max},
                    "reputation": reputation,
                    "refresh_time": refresh_time
                },
                "opponents": opponents,
                "counters": {
                    "challenge_count": challenge_count,
                    "extra_count": extra_count,
                    "next_challenge_cd": next_cd
                }
            }
        
        # 标记地点
        局_标记地点 = self.地图 + '|' + self.房间
        if '禅' in 局_标记地点 or '道' in 局_标记地点:
            self.send_orders('gto 盖亚之城驿站')
        elif '工厂' in 局_标记地点:
            self.gto('map.mopcity|五湖黑店')

        # 执行开始前函数
        _开始前函数()

        # 开始挑战循环，最多20次
        挑战次数 = 0
        最大挑战次数 = 20

        while 挑战次数 < 最大挑战次数:
            # 获取竞技场信息
            res = requests.get(f'http://{self.服务器}.pet.imop.com/jingjichang/jingjichang.jsp', cookies=self.cookies)
            res.encoding = 'gbk'
            _dict = parse_arena_html(res.text)

            # 检查挑战次数
            challenge_count = _dict['counters']['challenge_count']
            extra_count = _dict['counters']['extra_count']

            # 判断是否还有挑战次数
            has_normal_challenge = challenge_count and challenge_count['used'] < challenge_count['max']
            has_extra_challenge = extra_count and extra_count['used'] < extra_count['max']

            if not has_normal_challenge and not has_extra_challenge:
                self.print(f'竞技场挑战次数已用完，本次共完成 {挑战次数} 次挑战')
                break

            # 显示当前挑战次数状态
            if challenge_count:
                self.print(f'普通挑战次数: {challenge_count["used"]}/{challenge_count["max"]}')
            if extra_count:
                self.print(f'额外挑战次数: {extra_count["used"]}/{extra_count["max"]}')

            # 选择目标对手
            target = _dict['opponents'][0]
            for opponent in _dict['opponents'][:-1]:
                if opponent['rank'] > target['rank']:
                    target = opponent

            self.print(f'第 {挑战次数 + 1} 次挑战目标: {target["name"]} (排名: {target["rank"]})')
            self.send_orders('docompete ' + str(target['attack_id']))

            # 等待战斗结束
            while self.战斗状态:
                time.sleep(1)

            挑战次数 += 1
            self.print(f'第 {挑战次数} 次挑战完成')

            # 短暂等待，避免请求过快
            time.sleep(2)

        # 执行结束后函数
        _结束后函数()

        # 回到标记地点
        self.gto(局_标记地点)
        return
         
    def 特殊_清理经验(self):
        def _登录工具号():
            # 局_工具config = 
            工具_对象 = MYJ()
        pass

    def 活动_如意(self):
        _markpoint = self.地图 + '|' + self.房间        # 标记当前位置
        self.gto('map.mopcity|添香楼')
        self.send_orders('getrybp 2')
        self.gto(_markpoint)

    def 闲时_禅壹(self):
        if f'{self.地图}|{self.房间}' != 'chan|禅壹':
            self.gto('chan|禅壹')

        while True:
            # 检查任务中断信号
            if self._检查任务中断信号():
                self.print('收到任务中断信号，退出禅壹任务')
                return

            # 检查并执行竞技场
            if self._检查并执行竞技场():
                # 竞技场执行完毕，重新回到禅壹位置
                if f'{self.地图}|{self.房间}' != 'chan|禅壹':
                    self.gto('chan|禅壹')

            # 执行禅壹任务
            self.send_orders('fetchtimeprize')

            # 等待一段时间，避免频繁检查
            time.sleep(30)

    def 闲时_禅伍(self):
        if f'{self.地图}|{self.房间}' != 'chan|禅伍':
            self.gto('chan|禅伍')

        while True:
            # 检查任务中断信号
            if self._检查任务中断信号():
                self.print('收到任务中断信号，退出禅伍任务')
                return

            # 检查并执行竞技场
            if self._检查并执行竞技场():
                # 竞技场执行完毕，重新回到禅伍位置
                if f'{self.地图}|{self.房间}' != 'chan|禅伍':
                    self.gto('chan|禅伍')

            # 执行禅伍任务
            self.send_orders('fetchtimeprize')

            # 等待一段时间，避免频繁检查
            time.sleep(30)

    def 闲时_道壹(self):
        if f'{self.地图}|{self.房间}' != 'dao|道壹':
            self.gto('dao|道壹')

        while True:
            # 检查任务中断信号
            if self._检查任务中断信号():
                self.print('收到任务中断信号，退出道壹任务')
                return

            # 检查并执行竞技场
            if self._检查并执行竞技场():
                # 竞技场执行完毕，重新回到道壹位置
                if f'{self.地图}|{self.房间}' != 'dao|道壹':
                    self.gto('dao|道壹')

            # 执行道壹任务
            self.send_orders('fetchtimeprize')

            # 等待一段时间，避免频繁检查
            time.sleep(30)

    def 闲时_打工(self):
        if f'{self.地图}|{self.房间}' != 'map.all.other.GongChang|工厂':
            self.gto('map.all.other.GongChang|工厂')

        while True:
            # 检查任务中断信号
            if self._检查任务中断信号():
                self.print('收到任务中断信号，退出打工任务')
                return

            # 检查并执行竞技场
            if self._检查并执行竞技场():
                # 竞技场执行完毕，重新回到工厂位置
                if f'{self.地图}|{self.房间}' != 'map.all.other.GongChang|工厂':
                    self.gto('map.all.other.GongChang|工厂')

            # 执行打工任务
            self.send_orders('fetchtimeprize')

            # 等待一段时间，避免频繁检查
            time.sleep(30)

    def 闲时_刷怪(self):
        # 设置1小时时间限制
        刷怪开始时间 = time.time()
        最大运行时间 = 3600  # 1小时 = 3600秒

        def _刷怪_单次刷怪():
            # 检查是否有目标怪物
            局_Npc列表拷贝 = self.Npc列表.copy()
            局_Npc列表拷贝.reverse()        # 逆转列表,逆序开怪
            for 局_npc in 局_Npc列表拷贝:    # 对当前怪物房间遍历
                for 局_target in self.刷怪配置['刷怪目标']:
                    if (局_target in 局_npc[1] and
                        局_npc[3].find('尸体') < 0 and
                        局_npc[3].find('活动') < 0 and
                        局_npc[3].find('BOSS') < 0 and
                        局_npc[3].find('战斗中') < 0):    # 如果目标在怪物名称里
                        match self.刷怪配置.get('伏击技能', None):
                            case None:
                                局_开怪命令 = f'bar34 {局_npc[0]} {局_npc[2]}'
                            case _:
                                局_开怪命令 = f'perform {self.刷怪配置["伏击技能"]} {局_npc[0]}'
                        if 局_开怪命令 == self.最新命令:
                            continue
                        self.send_orders(局_开怪命令)
                        return True
            return False

        def _刷怪_清空房间():
            while True:
                # 检查任务中断信号
                if self._检查任务中断信号():
                    return
                if _刷怪_单次刷怪():
                    while self.战斗状态:
                        # 在战斗等待中也检查中断信号
                        if self._检查任务中断信号():
                            return
                        time.sleep(0.1)
                else:
                    break

        def _刷怪_处理背包():
            self.gto('map.maoyin|装备店')
            self.send_orders('fetchtimeprize')                      # 领取每日时间礼包
            局_处理次数 = 0
            局_开始时间 = time.time()
            局_最大等待时间 = 30  # 最多等待30秒

            print('开始处理背包')
            while (self.背包道具更新时间 is None) or (len(self.背包道具) == 0) or (time.time() - self.背包道具更新时间 > 5) and (局_处理次数 < 10):
                # 检查任务中断信号
                if self._检查任务中断信号():
                    self.print('处理背包时收到中断信号，退出')
                    return

                # 检查超时
                if time.time() - 局_开始时间 > 局_最大等待时间:
                    self.print(f'处理背包超时({局_最大等待时间}秒)，强制退出')
                    break

                self.print(f'背包道具更新时间: {self.背包道具更新时间}, 尝试次数: {局_处理次数}')
                self.print(f'检测到的背包道具数量: {len(self.背包道具)}')
                self.send_orders('i')  # 增加等待时间
            print('停止i处理背包')
            if time.time() - self.背包道具更新时间 < 5:
                局_背包道具列表 = self.背包道具.copy()
                局_长售卖代码 = ''
                局_背包道具列表.reverse()
                for 局_道具 in 局_背包道具列表:
                    for 局_需要处理的物品开头名称 in self.刷怪配置['售卖道具列表']:
                        if 局_道具['name'].startswith(局_需要处理的物品开头名称):
                            # 确保index和quantity不为None
                            index = 局_道具.get('index', '')
                            quantity = 局_道具.get('quantity', '')
                            if index and quantity:
                                局_长售卖代码 += f'sell {index} {quantity} OK|'
                            break
                self.print(局_长售卖代码)
                self.send_orders(局_长售卖代码)
            else:
                self.print('启用备用方案,从200至0索引')
                for i in range(200, 100, -1):
                    self.道具说明信息更新时间 = None
                    while self.道具说明信息更新时间 is None or (time.time() - self.道具说明信息更新时间 > 5):
                        self.send_orders(f'helptools {i}')
                        for 局_需要处理的物品开头名称 in self.刷怪配置['售卖道具列表']:
                            if self.道具说明信息['name'].startswith(局_需要处理的物品开头名称):
                                self.send_orders(f'sell {i} 1 OK')

        # 判断配置开始,检测刷怪配置是否完整,如果不完整的话,切换为禅壹
        if self.刷怪配置 is None:
            self.print('无刷怪配置,切换闲时任务为禅壹')
            self.闲时任务 = '闲时_禅壹'
            return
        if self.刷怪配置.get('刷怪起点') is None:
            self.print('无刷怪起点,切换闲时任务为禅壹')
            self.闲时任务 = '闲时_禅壹'
            return
        if self.刷怪配置.get('刷怪路线') is None:
            self.print('无刷怪路线,切换闲时任务为禅壹')
            self.闲时任务 = '闲时_禅壹'
            return
        if self.刷怪配置.get('刷怪目标') is None:
            self.print('无刷怪目标,切换闲时任务为禅壹')
            self.闲时任务 = '闲时_禅壹'
            return
        # 判断配置结束

        # 前置行为,装备技能,装备武器,装备店修理,修女治疗
        # 更新当前装备技能
        self.功能_装备指定技能(self.刷怪配置['职业技能'])
        # 更新当前武器
        self.功能_装备指定武器(self.换装包前缀, self.刷怪配置['刷怪武器'])
        # 前置行为装备店修理,修女治疗
        _刷怪_处理背包()
        self.send_orders('xiuli all|gto 教堂|zhiliao queding')

        self.gto(self.刷怪配置['刷怪起点'])
        #  初始化刷怪路线
        局_实用路线对象 = MyjRoute(self.刷怪配置['刷怪路线'])
        局_实用路线对象.now = 局_实用路线对象.head
        局_处理道具圈数 = self.刷怪配置.get('跑图多少圈进行一次售卖', 50)
        # 回到刷怪起点


        # 循环刷怪开始
        局_处理道具圈数计数 = 0
        while True:
            # 检查时间限制 - 超过1小时自动退出
            if time.time() - 刷怪开始时间 > 最大运行时间:
                self.print('闲时刷怪已运行1小时，自动退出等待下次调度')
                return

            # 检查任务中断信号 - 协作式退出机制
            if self._检查任务中断信号():
                self.print('收到任务中断信号，闲时刷怪优雅退出')
                return

            # 检查并执行竞技场
            if self._检查并执行竞技场():
                # 竞技场执行完毕，重新回到当前刷怪位置
                当前目标房间 = 局_实用路线对象.now.room if 局_实用路线对象.now else self.刷怪配置['刷怪起点']
                if self.房间 != 当前目标房间:
                    self.gto(当前目标房间)
            # 当前房间并非路线的最后一间,默认走到下一个节点
            if 局_实用路线对象.now  != 局_实用路线对象.tail:
                局_实用路线对象.now = 局_实用路线对象.now.next  # 地图节点切换到下一个节点
                # self.print(f'下一房间{局_实用路线对象.now.room}')
                尝试次数 = 0  # 修复：将计数器移到循环外部
                while True:
                    if 尝试次数 > 10:
                        self.print(f'房间移动失败超过10次，重置到刷怪起点')
                        self.gto(self.刷怪配置['刷怪起点'])
                        局_实用路线对象.now = 局_实用路线对象.head
                        break
                    self.send_orders(f'gto {局_实用路线对象.now.room}') # 走过去
                    if self.房间 == 局_实用路线对象.now.room:
                        break
                    else:
                        尝试次数 += 1
                        self.print(f'房间移动失败，尝试次数：{尝试次数}')
                        self.send_orders('look')
                _刷怪_清空房间()
            else:
                # 当前房间是路线的最后一间
                # 在这里判断圈数,进行处理背包
                if 局_处理道具圈数计数 < 局_处理道具圈数:
                    局_处理道具圈数计数 += 1                    # 没到清理圈数,计数+1
                else:
                    局_标记位置 = f'{self.地图}|{self.房间}'    # 标记当前位置
                    _刷怪_处理背包()
                    self.send_orders('xiuli all|gto 教堂|zhiliao queding')
                    局_长售卖代码 = ''
                    self.gto(局_标记位置)                       # 回到标记点
                    局_处理道具圈数计数 = 0
                # 背包处理完成
                局_实用路线对象.now = 局_实用路线对象.head      # 地图节点切换到头结点
                # 跑图完成一轮,回到起点之前执行额外命令
                if self.刷怪配置.get('每圈结束执行的其他代码') is not None:
                    self.send_orders(self.刷怪配置['每圈结束执行的其他代码'])
                # 回到起点，添加超时保护
                if self.房间 == 局_实用路线对象.tail.room:
                    回到起点尝试次数 = 0
                    while True:
                        if 回到起点尝试次数 > 10:
                            self.print('回到起点失败超过10次，使用gto强制回到起点')
                            self.gto(self.刷怪配置['刷怪起点'])
                            break
                        self.send_orders(f'gto {局_实用路线对象.head.room}') # 走过去
                        if self.房间 == 局_实用路线对象.head.room:
                            break
                        else:
                            回到起点尝试次数 += 1
                            self.print(f'回到起点失败，尝试次数：{回到起点尝试次数}')
                            self.send_orders('look')
                else:
                    self.gto(self.刷怪配置['刷怪起点'])
            # 进入清理怪物函数

    def 活动_通天(self):
        # 更新当前装备技能
        self.功能_装备指定技能(self.通天配置['职业技能'])
        self.功能_装备指定武器(self.换装包前缀, self.通天配置['刷怪武器'])
        局_完成标记 = False
        while True:
            if datetime.datetime.now().hour >= 21:
                self.print('通天任务已超过21点，自动退出')
                return
            if (datetime.datetime.now().minute % 3 == 0) and (datetime.datetime.now().second <= 20) and (self.房间 == '通天塔'):
                self.send_orders('look')
            # 当前房间不包含通天塔字样,不在指定位置
            if '通天塔' not in self.房间:
                self.gto('map.baimagang|通天塔')
            # 当前房间在通天塔门口
            if self.房间 == '通天塔':
                self.send_orders('comein yes', 0.28)
                continue
            if '通天塔' in self.房间 and '层' in self.房间:
                if '五十' in self.房间 or '50' in self.房间:
                    局_完成标记 = True
                局_NPC逆序列表 = self.Npc可攻击列表.copy()
                if len(局_NPC逆序列表) > 0:
                    局_NPC逆序列表.reverse()
                    self.send_orders(f'bar34 {局_NPC逆序列表[0][0]} {局_NPC逆序列表[0][2]}', 0.25)
                    while self.战斗状态:
                        time.sleep(0.1)
                    self.send_orders('gotonext')
                    if 局_完成标记:
                        return
            time.sleep(0.05)

    def 活动_逆无双(self):
        self.功能_装备指定技能(self.逆无双配置['职业技能'])
        self.功能_装备指定武器(self.换装包前缀, self.逆无双配置['刷怪武器'])
        局_杀怪数量字典 = {
            1:5,
            2:5,
            3:10,
            4:10,
            5:15,
            6:15,
            7:20,
            8:20,
            9:25,
            10:30
        }
        if '通天塔' not in self.房间:
            self.gto('map.baimagang|通天塔')        # 不在逆无双入口,走到白马-通天塔
        while self.房间 == '通天塔':
            self.send_orders('comeinNWST yes')
        bar_code = ''   # 初始化一个空字符串,用作打BOSS用
        while (bar_code != 'tiaozhan地穴领主') and ('逆无双' in self.房间):   # 当打BOSS不是地穴领主时,继续循环
            if datetime.datetime.now().hour >= 21:
                self.print('逆无双任务已超过21点，自动退出')
                return
            if '逆无双' in self.房间 and '层' in self.房间:
                # 当前在塔里
                # 获取当前所在层
                局_文本 = self.房间[5:7]
                if '层' in 局_文本:
                    局_当前层 = int(局_文本[:-1])
                else:
                    局_当前层 = int(局_文本)
                局_当前房间尾号 = int(self.房间[-2:])
                局_楼层前缀 = self.房间[:-2]
                if 局_杀怪数量字典[局_当前层] > 0:  # 仍需击杀小怪
                    if 局_当前房间尾号 >= 9:        # 格子大于10的时候,回到房间1
                        局_当前房间尾号 = 1
                    else :
                        局_当前房间尾号 += 1
                    self.send_orders(f'gto {局_楼层前缀}{局_当前房间尾号:02}')
                    局_怪物列表 = [怪物 for 怪物 in self.Npc列表.copy() if ('尸体' not in 怪物[3] and '战斗中' not in 怪物[3])]
                    if len(局_怪物列表) > 0:    # 当前房间有怪
                        局_怪物列表.reverse()   # 逆序
                        for 局_怪物 in 局_怪物列表:
                            self.send_orders(f'bar34 {局_怪物[0]} {局_怪物[2]}' + '|lookatt')
                            if self.战斗状态:
                                局_杀怪数量字典[局_当前层] -= 1
                                while self.战斗状态:
                                    time.sleep(0.1)
                                break
                    else:                       # 当前房间没怪
                        if 局_当前房间尾号 >= 10:
                            self.send_orders(f'gto {局_楼层前缀}01')   # 回到01
                        else:   # 尾号小于10
                            self.send_orders(f'gto {局_楼层前缀}{局_当前房间尾号 + 1:02}')
                        continue    # 走完房间以后continue一下,进入下一个循环,下一个循环应该是继续监测怪物
                else:           # 数量字典值为0,代表当前层杀够数量了
                    while f'16' not in self.房间:   # 如果不在BOSS房间,就一直走
                        self.send_orders(f'gto {self.房间[:-2]}16|look')
                    # self.send_orders(f'gto {self.房间[:-2]}16') # 走到BOSS房间
                    match 局_当前层:
                        case 1:
                            bar_code = 'tiaozhan露西法的镜像'
                        case 2:
                            bar_code = 'tiaozhan小晴的镜像'
                        case 3:
                            bar_code = 'tiaozhan血魔王'
                        case 4:
                            bar_code = 'tiaozhan吸血魔王'
                        case 5:
                            bar_code = 'tiaozhan地狱骑士'
                        case 6:
                            bar_code = 'tiaozhan月亮上的少女'
                        case 7:
                            bar_code = 'tiaozhan魔龙人'
                        case 8:
                            bar_code = 'tiaozhan加湿用烘干器'
                        case 9:
                            bar_code = 'tiaozhan波瑞阿斯.冰暴'
                        case 10:
                            bar_code = 'tiaozhan地穴领主'
                    # while not self.战斗状态:        # 进入战斗
                    self.send_orders(bar_code)
                    while self.战斗状态:            # 等待战斗结束
                        time.sleep(0.1)
                    局_房间 = self.房间
                    while self.房间 == 局_房间:
                        self.send_orders('upstairs')    # 打完怪,上楼

    def 活动_普通无双(self):
        self.功能_装备指定技能(self.逆无双配置['职业技能'])
        self.功能_装备指定武器(self.换装包前缀, self.逆无双配置['刷怪武器'])
        局_杀怪数量字典 = {
            1:5,
            2:5,
            3:10,
            4:10,
            5:15,
            6:15,
            7:20,
            8:20,
            9:25,
            10:30
        }
        if '通天塔' not in self.房间:
            self.gto('map.baimagang|通天塔')        # 不在逆无双入口,走到白马-通天塔
        while self.房间 == '通天塔':
            self.send_orders('comeinWuShuangTa yes')
        bar_code = ''   # 初始化一个空字符串,用作打BOSS用
        while (bar_code != 'tiaozhan露西法的镜像') and ('无双' in self.房间):   # 当打BOSS不是地穴领主时,继续循环
            if datetime.datetime.now().hour >= 21:
                self.print('普通无双任务已超过21点，自动退出')
                return
            if '无双塔' in self.房间 and '层' in self.房间:
                # 当前在塔里
                # 获取当前所在层
                局_文本 = self.房间[4:6]
                if '层' in 局_文本:
                    局_当前层 = int(局_文本[:-1])
                else:
                    局_当前层 = int(局_文本)
                局_当前房间尾号 = int(self.房间[-2:])
                局_楼层前缀 = self.房间[:-2]
                if 局_杀怪数量字典[局_当前层] > 0:  # 仍需击杀小怪
                    if 局_当前房间尾号 >= 9:        # 格子大于10的时候,回到房间1
                        局_当前房间尾号 = 1
                    else :
                        局_当前房间尾号 += 1
                    self.send_orders(f'gto {局_楼层前缀}{局_当前房间尾号:02}')
                    局_怪物列表 = [怪物 for 怪物 in self.Npc列表.copy() if ('尸体' not in 怪物[3] and '战斗中' not in 怪物[3])]
                    if len(局_怪物列表) > 0:    # 当前房间有怪
                        局_怪物列表.reverse()   # 逆序
                        for 局_怪物 in 局_怪物列表:
                            self.send_orders(f'bar34 {局_怪物[0]} {局_怪物[2]}' + '|lookatt')
                            if self.战斗状态:
                                局_杀怪数量字典[局_当前层] -= 1
                                while self.战斗状态:
                                    time.sleep(0.1)
                                break
                    else:                       # 当前房间没怪
                        if 局_当前房间尾号 >= 10:
                            self.send_orders(f'gto {局_楼层前缀}01')   # 回到01
                        else:   # 尾号小于10
                            self.send_orders(f'gto {局_楼层前缀}{局_当前房间尾号 + 1:02}')
                        continue    # 走完房间以后continue一下,进入下一个循环,下一个循环应该是继续监测怪物
                else:           # 数量字典值为0,代表当前层杀够数量了
                    while f'16' not in self.房间:   # 如果不在BOSS房间,就一直走
                        self.send_orders(f'gto {self.房间[:-2]}16|look')
                    match 局_当前层:
                        case 10:
                            bar_code = 'tiaozhan露西法的镜像'
                        case 9:
                            bar_code = 'tiaozhan小晴的镜像'
                        case 8:
                            bar_code = 'tiaozhan血魔王'
                        case 7:
                            bar_code = 'tiaozhan吸血魔王'
                        case 6:
                            bar_code = 'tiaozhan地狱骑士'
                        case 5:
                            bar_code = 'tiaozhan月亮上的少女'
                        case 4:
                            bar_code = 'tiaozhan魔龙人'
                        case 3:
                            bar_code = 'tiaozhan加湿用烘干器'
                        case 2:
                            bar_code = 'tiaozhan波瑞阿斯.冰暴'
                        case 1:
                            bar_code = 'tiaozhan地穴领主'
                    self.send_orders(bar_code)
                    while self.战斗状态:            # 等待战斗结束
                        time.sleep(0.1)
                    局_房间 = self.房间
                    while self.房间 == 局_房间:
                        self.send_orders('upstairs')    # 打完怪,上楼

    def 活动_罗汉(self):
        pass

    def 特殊_大懒怪(self):
        while len(self.Npc列表) == 0:
            self.send_orders('look')
            time.sleep(0.1)
        while not self.战斗状态:
            for i in self.Npc列表.copy():
                if '大懒怪' in i[1]:
                    self.send_orders(f'bar34 {i[0]} {i[2]}')
                    break
        while self.战斗状态:
            self.send_orders('perform 出血[7级]|冥火爪[11级', 0.25)
        # keyboard.wait(';')

    def 功能_装备指定技能(self, 技能名称):
        # 更新当前装备技能
        while self.技能列表更新时间 is None or (time.time() - self.技能列表更新时间 > 5):
            self.send_orders('subs')
            for 局_技能列表 in self.技能列表:
                if 局_技能列表[0] == 技能名称:
                    if 局_技能列表[2][-2] is False:
                        self.print(f'未装备{技能名称},发送装备命令')
                        self.send_orders(f'equipskills {技能名称}')
                    else:
                        self.print(f'正在装备技能:{技能名称}')

    def 功能_装备指定武器(self, 换装包名称, 武器名称):
        if 换装包名称 is not None:
            while self.背包道具更新时间 is None or (time.time() - self.背包道具更新时间 > 5):
                self.send_orders('i',0.32)
            for 局_背包道具 in self.背包道具:
                if 局_背包道具['name'].startswith(换装包名称):
                    while self.道具说明信息更新时间 is None or (time.time() - self.道具说明信息更新时间 > 5):
                        self.send_orders(f'helptools {局_背包道具['index']}')
                    if self.道具说明信息['name'].startswith(换装包名称) and 武器名称 in self.道具说明信息['description']:
                        self.send_orders(f'use {局_背包道具['index']} equipall')
                    else:
                        self.print(f'{换装包名称}中无目标{武器名称},默认已装备')

    def 每日循环(self):
        pass

    def 主函数(self):
        """
        主任务调度函数 - 基于时间驱动的任务管理
        """

        def 执行日常任务():
            """执行所有启用的日常任务"""
            self.print('=== 开始执行日常任务 ===')

            # 根据配置获取需要执行的日常任务
            需要执行的任务 = []
            for 任务配置 in self.日常任务配置:
                任务名 = 任务配置['任务名']
                开关属性 = 任务配置['开关属性']
                任务函数 = 任务配置['执行函数']
                优先级 = 任务配置['优先级']

                # 检查开关是否启用
                if eval(开关属性):
                    需要执行的任务.append((优先级, 任务名, 任务函数))

            # 按优先级排序执行
            需要执行的任务.sort(key=lambda x: x[0])

            # 依次执行日常任务
            for 优先级, 任务名, 任务函数 in 需要执行的任务:
                self.print(f'执行日常任务: {任务名} (优先级: {优先级})')
                try:
                    任务函数()
                    self.print(f'日常任务 {任务名} 完成')
                except Exception as e:
                    self.print(f'日常任务 {任务名} 执行异常: {e}')

            self.print('=== 日常任务全部完成 ===')

        def 检查活动时间():
            """检查当前是否为活动时间，返回活动名称和函数，或None"""
            现在 = datetime.datetime.now()
            当前星期 = 现在.weekday()  # 0=周一, 6=周日
            当前时间 = 现在.time()
            今天 = 现在.date().isoformat()

            # 收集所有可执行的活动任务
            可执行活动 = []

            for 任务配置 in self.活动任务配置:
                任务名 = 任务配置['任务名']
                任务函数 = 任务配置['执行函数']
                优先级 = 任务配置['优先级']

                if 任务名 == '如意':
                    # 如意活动特殊处理
                    开关属性 = 任务配置.get('开关属性')
                    if 开关属性 and eval(开关属性):
                        时间段列表 = 任务配置['时间段']
                        for 开始时间, 结束时间, 时间段标识 in 时间段列表:
                            if 开始时间 <= 当前时间 <= 结束时间:
                                执行标识 = f"{今天}_{时间段标识}"
                                if 执行标识 not in self.今日已执行活动:
                                    可执行活动.append((优先级, 时间段标识, 任务函数))
                                break
                else:
                    # 其他活动任务
                    开关属性 = 任务配置.get('开关属性')
                    if 开关属性 and eval(开关属性):
                        允许星期 = 任务配置['星期']
                        开始时间 = 任务配置['开始时间']
                        结束时间 = 任务配置['结束时间']

                        if (当前星期 in 允许星期 and 开始时间 <= 当前时间 <= 结束时间):
                            执行标识 = f"{今天}_{任务名}"
                            if 执行标识 not in self.今日已执行活动:
                                可执行活动.append((优先级, 任务名, 任务函数))

            # 按优先级排序，返回最高优先级的活动
            if 可执行活动:
                可执行活动.sort(key=lambda x: x[0])
                优先级, 活动名, 任务函数 = 可执行活动[0]
                return (活动名, 任务函数)

            return None

        def 是否日期切换时间():
            """检查是否为日期切换时间 (5:30-5:35)"""
            现在 = datetime.datetime.now()
            当前时间 = 现在.time()

            # 添加调试日志
            self.print(f'当前时间检查: {当前时间}')

            # 5:30-5:35 (游戏日期切换时间)
            if datetime.time(5, 30) <= 当前时间 <= datetime.time(5, 35):
                self.print('检测到日期切换时间')
                return True
            return False

        while not self.cookies:
            time.sleep(1)
        # 主循环 - 每天的任务周期
        while True:
            try:
                self.print(f'\n=== 新的一天开始 {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")} ===')

                # 清空今日已执行活动记录
                self.今日已执行活动.clear()
                time.sleep(5)
                # 1. 执行日常任务
                执行日常任务()

                # 2. 进入时间段循环
                当前闲时线程 = None
                当前活动线程 = None

                while True:
                    # 检查是否为日期切换时间
                    if 是否日期切换时间():
                        self.print('进入日期切换时间，协作式停止所有任务...')
                        if 当前闲时线程 and 当前闲时线程.is_alive():
                            # 协作式停止闲时任务
                            if self._发送任务中断信号():
                                if not self._等待任务优雅退出(最大等待秒数=10):
                                    self.print('闲时任务未能及时退出，强制销毁')
                                    当前闲时线程.destroy()
                            当前闲时线程 = None

                        # 等待到切换时间结束，添加超时保护
                        等待开始时间 = time.time()
                        最大等待时间 = 600  # 最多等待10分钟
                        while 是否日期切换时间():
                            if time.time() - 等待开始时间 > 最大等待时间:
                                self.print('日期切换等待超时，强制继续')
                                break
                            time.sleep(30)  # 每30秒检查一次

                        self.print('日期切换完成，进入新的一天')
                        break  # 跳出时间段循环，进入新一天

                    # 主动清理已完成的线程
                    if 当前活动线程 and not 当前活动线程.is_alive():
                        self.print(f'检测到活动线程已完成，清理线程引用')
                        当前活动线程 = None

                    if 当前闲时线程 and not 当前闲时线程.is_alive():
                        self.print(f'检测到闲时线程已完成，清理线程引用')
                        当前闲时线程 = None

                    # 检查活动时间
                    活动任务 = 检查活动时间()

                    if 活动任务:
                        活动名, 活动函数 = 活动任务

                        # 确保同时只有一个线程运行：优先活动线程
                        if not 当前活动线程:
                            # 协作式停止闲时任务，为活动任务让路
                            if 当前闲时线程:
                                self.print(f'协作式停止闲时任务，准备执行活动: {活动名}')
                                # 发送中断信号并等待优雅退出
                                if self._发送任务中断信号():
                                    if self._等待任务优雅退出(最大等待秒数=15):
                                        self.print('闲时任务已优雅退出')
                                    else:
                                        self.print('闲时任务未能及时退出，强制销毁')
                                        当前闲时线程.destroy()
                                当前闲时线程 = None

                            # 立即标记该活动今日已执行，防止重复执行
                            今天 = datetime.datetime.now().date().isoformat()
                            执行标识 = f"{今天}_{活动名}"
                            self.今日已执行活动[执行标识] = True

                            # 执行活动任务 - 线程化执行，避免阻塞主调度循环
                            self.print(f'开始执行活动任务: {活动名}')

                            def _执行活动任务():
                                try:
                                    # 使用任务包装器执行活动函数
                                    self._任务包装器(活动函数, f'活动_{活动名}')
                                    self.print(f'活动任务 {活动名} 完成')

                                except Exception as e:
                                    self.print(f'活动任务 {活动名} 执行异常: {e}')
                                    # 如果执行异常，移除标记，允许重试
                                    if 执行标识 in self.今日已执行活动:
                                        del self.今日已执行活动[执行标识]

                            # 创建活动任务线程
                            当前活动线程 = MyThread(target=_执行活动任务, name=f'activity-{活动名}')
                            当前活动线程.start()
                        else:
                            self.print(f'活动线程 {当前活动线程.name} 正在运行，跳过新活动 {活动名}')

                    else:
                        # 非活动时间，执行闲时任务
                        # 确保同时只有一个线程运行：只有在没有活动线程和闲时线程时才启动
                        if not 当前活动线程 and not 当前闲时线程:
                            if self.闲时任务:
                                self.print(f"准备启动闲时任务，当前配置: {self.闲时任务}")
                                # 根据配置查找对应的闲时任务
                                for 任务配置 in self.闲时任务配置:
                                    配置值 = 任务配置['配置值']
                                    任务名 = 任务配置['任务名']
                                    任务函数 = 任务配置['执行函数']

                                    if self.闲时任务 == 配置值:
                                        self.print(f'开始执行闲时任务: {任务名} ({配置值})')
                                        # 使用任务包装器执行闲时任务
                                        def _执行闲时任务():
                                            self._任务包装器(任务函数, f'闲时_{任务名}')
                                        当前闲时线程 = MyThread(target=_执行闲时任务, name=f'idle-{任务名}')
                                        当前闲时线程.start()
                                        break
                                else:
                                    self.print(f'未找到配置的闲时任务: {self.闲时任务}')
                            else:
                                self.print("没有配置闲时任务")
                        else:
                            # 显示当前线程状态，便于调试
                            活动状态 = f"活动线程: {当前活动线程.name if 当前活动线程 else 'None'}"
                            闲时状态 = f"闲时线程: {当前闲时线程.name if 当前闲时线程 else 'None'}"
                            self.print(f"当前有线程在运行，跳过闲时任务启动 - {活动状态}, {闲时状态}")

                    # 每分钟检查一次
                    time.sleep(60)

            except Exception as e:
                self.print(f'主函数异常: {e}')
                time.sleep(60)

    def __del__(self):
        if self.online_thread and self.online_thread._state != ThreadState.DESTROYED:
            self.online_thread.destroy()
        self.print('线程已销毁')


def function_test():
    config = {
        "服务器": "x12",
        "账号": "1425661289",
        "密码": "RgXyy1024.",
        "角色序号": "0",
        "代理配置": {
            "代理账号":"FQTUENGM",
            "代理密码":"35B7E37DAF97"
        },
        "邮箱": "<EMAIL>",
        "卡级配置": 80,
        "日常拖把开关": True,
        "日常魔化开关": False,
        "日常诅咒小镇开关": False,
        "活动如意开关": True,
        "活动通天开关": True,
        "活动逆无双开关": False,
        "活动罗汉开关": False,
        "竞技场开关": True,
        "闲时任务": "闲时_禅伍",
        "换装包前缀": "刷怪换装包",
        "通天配置": {
                "刷怪武器": "强击长矛",
                "职业技能": "霸王枪",
                "释放技能列表": ["猛抽", "致命一击", "强力打击[1级]"]
                },
        "刷怪配置": {
                "刷怪武器": "刺客之刃",
                "职业技能": "龙葵匕刃",
                "释放技能列表": ["强力打击[1级]","凿击[1级]", "猛抽", "致命一击"],
                "刷怪起点": "map.kasitepingyuan|卡斯特平原34",
                "刷怪路线": "卡斯特平原34,卡斯特平原12,卡斯特平原15,卡斯特平原41,卡斯特平原19,卡斯特平原42,卡斯特平原21,卡斯特平原17,卡斯特平原18,卡斯特平原16,卡斯特平原36,卡斯特平原35",
                "刷怪目标": ["豆芽", "蚱蜢", "螃蟹"],
                "伏击技能": "伏击[一级]",
                "每圈结束执行的其他代码": None,
                "跑图多少圈进行一次售卖": 200,
                "售卖道具列表":["猎鹰之", "巨猿之", "野猪之","猛虎之", "达拉", "宝箱","民兵","针织","螃蟹","亚麻","普通","农夫","园艺","能量之","敏捷之","鼓舞","坚韧","精神","奥术","猛击","炸弹","战斗怒吼","暴怒","残忍","雄鹰之","灵猴之","巨鲸之"]
            },
        "技能配置":{
                    "出血[6级]":{"前摇":0,"后摇":0,"CD":5,"剩余CD": 0},
                    "冥火爪[11级]":{"前摇":0,"后摇":0,"CD":10,"剩余CD": 0},
                    "凿击[1级]":{"前摇":0,"后摇":0,"CD":20,"剩余CD": 0},
                    "强力打击[1级]":{"前摇":3,"后摇":0,"CD":6,"剩余CD": 0},
                    "猛抽":{"前摇":0,"后摇":0,"CD":30,"剩余CD": 0},
                    "致命一击":{"前摇":3,"后摇":0,"CD":40,"剩余CD": 0}
            }

    }

    print('开始测试...')
    myj = MYJ(config)
    while myj.登入标记 == False:
        time.sleep(1)
    局_测试线程 = MyThread(target=myj.主函数, name=f'测试-{myj.账号}')
    # 局_测试线程 = MyThread(target=myj.日常_竞技场, name=f'测试-{myj.账号}')
    局_测试线程.start()
    input()
    # 运行主函数
    # myj.主函数()
    # myj.活动_通天()
    del myj

if __name__ == '__main__':
    function_test()
